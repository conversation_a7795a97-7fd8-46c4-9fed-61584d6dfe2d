"use client";
import React, { useMemo, useRef, useEffect, useState, useCallback } from "react";
import dynamic from "next/dynamic";
import { cn } from "@/lib/utils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import "quill/dist/quill.snow.css";
import "./headless-editor.css";
import {
  Bold,
  Italic,
  Underline,
  Strikethrough,
  Quote,
  Code,
  Link,
  Image,
  Video,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Palette,
  Type,
  Undo,
  Redo,
  MoreHorizontal,
  Heading1,
  Heading2,
  Heading3,
  Heading4,
  X
} from "lucide-react";
import "quill/dist/quill.snow.css";
import "./headless-editor.css";

// Color palette for text and background colors
const colorPalette = [
  '#000000', '#e60000', '#ff9900', '#ffff00', '#008a00', '#0066cc', '#9933ff',
  '#ffffff', '#facccc', '#ffebcc', '#ffffcc', '#cce8cc', '#cce0f5', '#ebd6ff',
  '#bbbbbb', '#f06666', '#ffc266', '#ffff66', '#66b966', '#66a3e0', '#c285ff',
  '#888888', '#a10000', '#b26b00', '#b2b200', '#006100', '#0047b2', '#6b24b2',
  '#444444', '#5c0000', '#663d00', '#666600', '#003700', '#002966', '#3d1466'
];

// Dynamically import ReactQuill with forwardRef support
const ReactQuill = dynamic(() => import("react-quill-new"), {
  ssr: false,
  loading: () => (
    <div className="quill-editor">
      <div className="h-32 bg-gray-50 border border-gray-200 rounded animate-pulse flex items-center justify-center">
        <span className="text-gray-400">Loading editor...</span>
      </div>
    </div>
  ),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
}) as React.ComponentType<any>;

// Headless toolbar configuration
const headlessToolbarGroups = [
  {
    name: "formatting",
    items: [
      { type: "bold", icon: Bold, label: "Bold" },
      { type: "italic", icon: Italic, label: "Italic" },
      { type: "underline", icon: Underline, label: "Underline" },
      { type: "strike", icon: Strikethrough, label: "Strikethrough" },
    ]
  },
  {
    name: "blocks",
    items: [
      { type: "blockquote", icon: Quote, label: "Quote" },
      { type: "code-block", icon: Code, label: "Code Block" },
      { type: "header", value: 1, icon: Heading1, label: "Heading 1" },
      { type: "header", value: 2, icon: Heading2, label: "Heading 2" },
      { type: "header", value: 3, icon: Heading3, label: "Heading 3" },
      { type: "header", value: 4, icon: Heading4, label: "Heading 4" },
    ]
  },
  {
    name: "lists",
    items: [
      { type: "list", value: "ordered", icon: ListOrdered, label: "Numbered List" },
      { type: "list", value: "bullet", icon: List, label: "Bullet List" },
    ]
  },
  {
    name: "alignment",
    items: [
      { type: "align", value: "", icon: AlignLeft, label: "Align Left" },
      { type: "align", value: "center", icon: AlignCenter, label: "Align Center" },
      { type: "align", value: "right", icon: AlignRight, label: "Align Right" },
      { type: "align", value: "justify", icon: AlignJustify, label: "Justify" },
    ]
  },
  {
    name: "colors",
    items: [
      { type: "color", icon: Palette, label: "Text Color" },
      { type: "background", icon: Type, label: "Background Color" },
    ]
  },
  {
    name: "media",
    items: [
      { type: "link", icon: Link, label: "Insert Link" },
      { type: "image", icon: Image, label: "Insert Image" },
      { type: "video", icon: Video, label: "Insert Video" },
    ]
  },
  {
    name: "actions",
    items: [
      { type: "undo", icon: Undo, label: "Undo" },
      { type: "redo", icon: Redo, label: "Redo" },
      { type: "more", icon: MoreHorizontal, label: "More Options" },
    ]
  }
];

interface ToolbarItem {
  type: string;
  value?: string | number;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
}

interface ActiveFormats {
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
  strike?: boolean;
  header?: number;
  blockquote?: boolean;
  'code-block'?: boolean;
  list?: string;
  align?: string;
  color?: string;
  background?: string;
  link?: string;
}

interface HeadlessToolbarProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  quillRef: React.RefObject<any>;
  isVisible: boolean;
  onClose: () => void;
  position: { top: number; left: number };
  activeFormats?: ActiveFormats;
}

interface ColorPickerProps {
  onColorSelect: (color: string) => void;
  onClose: () => void;
  type: 'text' | 'background';
  buttonRef?: React.RefObject<HTMLButtonElement>;
}

function ColorPicker({ onColorSelect, onClose, type, buttonRef }: ColorPickerProps) {
  const [position, setPosition] = useState({ top: '50%', left: '50%' });

  useEffect(() => {
    if (buttonRef?.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;

      // Position above the button if there's space, otherwise below
      const spaceAbove = rect.top;
      const spaceBelow = viewportHeight - rect.bottom;

      let top: string;
      let left: string;

      if (spaceAbove > 300) {
        // Position above the button
        top = `${rect.top - 10}px`;
      } else if (spaceBelow > 300) {
        // Position below the button
        top = `${rect.bottom + 10}px`;
      } else {
        // Center vertically if no good space
        top = '50%';
      }

      // Center horizontally relative to button, but keep within viewport
      const centerX = rect.left + rect.width / 2;
      if (centerX < 150) {
        left = '150px';
      } else if (centerX > viewportWidth - 150) {
        left = `${viewportWidth - 150}px`;
      } else {
        left = `${centerX}px`;
      }

      setPosition({ top, left });
    }
  }, [buttonRef]);

  return (
    <>
      {/* Backdrop to close color picker when clicking outside */}
      <div
        className="fixed inset-0 z-[10000] bg-transparent"
        onClick={onClose}
      />
      <Card
        className="fixed p-2 shadow-lg border bg-background min-w-[200px] max-w-[250px]"
        style={{
          zIndex: 10001,
          top: position.top,
          left: position.left,
          transform: position.top === '50%' ? 'translate(-50%, -50%)' : 'translateX(-50%)',
          maxHeight: '80vh',
          overflowY: 'auto'
        }}
      >
        <div className="mb-2">
          <p className="text-xs font-medium text-muted-foreground">
            {type === 'text' ? 'Text Color' : 'Background Color'}
          </p>
        </div>
        <div className="grid grid-cols-7 gap-1 mb-2">
          {colorPalette.map((color, index) => (
            <button
              key={index}
              type="button"
              className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
              style={{ backgroundColor: color }}
              onClick={() => {
                onColorSelect(color);
                onClose();
              }}
              title={color}
            />
          ))}
        </div>
        <div className="flex items-center gap-2 pt-2 border-t">
          <input
            type="color"
            className="w-8 h-6 rounded border cursor-pointer"
            onChange={(e) => {
              onColorSelect(e.target.value);
              onClose();
            }}
            title="Custom color"
          />
          <span className="text-xs text-muted-foreground">Custom</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="ml-auto h-6 w-6 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </Card>
    </>
  );
}

function HeadlessToolbar({ quillRef, isVisible, onClose, position, activeFormats = {} }: HeadlessToolbarProps) {
  const [showColorPicker, setShowColorPicker] = useState<'text' | 'background' | null>(null);
  const colorButtonRef = useRef<HTMLButtonElement>(null);
  const backgroundButtonRef = useRef<HTMLButtonElement>(null);

  // All hooks must be called before any early returns
  const handleColorSelect = useCallback((color: string, type: 'text' | 'background') => {
    console.log('🎨 handleColorSelect called with:', { color, type });

    if (!quillRef.current) {
      console.warn('🎨 No quillRef.current available');
      return;
    }

    const quill = quillRef.current.getEditor();
    if (!quill) {
      console.warn('🎨 No quill editor available');
      return;
    }

    const range = quill.getSelection();
    if (!range) {
      console.warn('🎨 No text selection available');
      return;
    }

    console.log('🎨 Applying color format:', { type, color, range });

    if (type === 'text') {
      quill.format('color', color);
    } else {
      quill.format('background', color);
    }

    // Close the color picker after selection
    setShowColorPicker(null);

    quill.focus();
  }, [quillRef]);

  const handleToolbarAction = useCallback((item: ToolbarItem) => {
    if (!quillRef.current) return;

    const quill = quillRef.current.getEditor();
    const range = quill.getSelection();

    if (!range) return;

    switch (item.type) {
      case "bold":
      case "italic":
      case "underline":
      case "strike":
        quill.format(item.type, !quill.getFormat(range)[item.type]);
        break;
      case "blockquote":
      case "code-block":
        quill.format(item.type, !quill.getFormat(range)[item.type]);
        break;
      case "header":
        const currentHeader = quill.getFormat(range).header;
        quill.format("header", currentHeader === item.value ? false : item.value);
        break;
      case "list":
        const currentList = quill.getFormat(range).list;
        quill.format("list", currentList === item.value ? false : item.value);
        break;
      case "align":
        quill.format("align", item.value || false);
        break;
      case "color":
        setShowColorPicker('text');
        return; // Don't close toolbar, let color picker handle it
      case "background":
        setShowColorPicker('background');
        return; // Don't close toolbar, let color picker handle it
      case "link":
        const url = prompt("Enter URL:");
        if (url) {
          quill.format("link", url);
        }
        break;
      case "image":
        const imageUrl = prompt("Enter image URL:");
        if (imageUrl) {
          quill.insertEmbed(range.index, "image", imageUrl);
        }
        break;
      case "video":
        const videoUrl = prompt("Enter video URL:");
        if (videoUrl) {
          quill.insertEmbed(range.index, "video", videoUrl);
        }
        break;
    }

    quill.focus();
  }, [quillRef]);

  // Debug: Log when toolbar renders with active formats
  console.log('🎨 HeadlessToolbar rendering with activeFormats:', activeFormats, 'isVisible:', isVisible);

  // Don't render if not visible (after all hooks)
  if (!isVisible) {
    return null;
  }

  // Helper function to check if a format is active
  const isFormatActive = (item: ToolbarItem): boolean => {
    const result = (() => {
      switch (item.type) {
        case 'bold':
        case 'italic':
        case 'underline':
        case 'strike':
        case 'blockquote':
        case 'code-block':
          return Boolean(activeFormats[item.type as keyof ActiveFormats]);
        case 'header':
          return activeFormats.header === item.value;
        case 'list':
          return activeFormats.list === item.value;
        case 'align':
          return activeFormats.align === item.value;
        case 'color':
          return Boolean(activeFormats.color);
        case 'background':
          return Boolean(activeFormats.background);
        default:
          return false;
      }
    })();

    console.log(`🎨 STYLING DEBUG - isFormatActive for ${item.type}${item.value ? ` (${item.value})` : ''}:`, result, 'activeFormats:', activeFormats);
    return result;
  };

  // Get button style with active state support
  const getButtonStyle = (isActive: boolean = false): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      border: 'none',
      padding: '6px',
      borderRadius: '4px',
      cursor: 'pointer',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      width: '32px',
      height: '32px',
      flexShrink: 0,
      transition: 'all 0.2s ease',
    };

    if (isActive) {
      return {
        ...baseStyle,
        backgroundColor: 'hsl(var(--primary))',
        color: 'hsl(var(--primary-foreground))',
        borderColor: 'hsl(var(--primary))',
        boxShadow: '0 0 0 1px hsl(var(--primary))',
      };
    }

    return {
      ...baseStyle,
      backgroundColor: 'transparent',
      color: 'inherit',
      borderColor: 'transparent',
    };
  };

  // Get button class name based on active state
  const getButtonClassName = (isActive: boolean) => {
    return isActive ? 'headless-toolbar-button-active' : '';
  };

  // Apply active styles with !important using DOM manipulation
  const applyActiveStyles = (element: HTMLButtonElement, isActive: boolean) => {
    if (isActive) {
      element.style.setProperty('background-color', 'hsl(var(--primary))', 'important');
      element.style.setProperty('color', 'hsl(var(--primary-foreground))', 'important');
      element.style.setProperty('border-color', 'hsl(var(--primary))', 'important');
      element.style.setProperty('box-shadow', '0 0 0 1px hsl(var(--primary))', 'important');
    } else {
      element.style.removeProperty('background-color');
      element.style.removeProperty('color');
      element.style.removeProperty('border-color');
      element.style.removeProperty('box-shadow');
    }
  };



  if (!isVisible) return null;

  return (
    <Card
      className="fixed z-50 p-2 shadow-lg border bg-background/95 backdrop-blur-sm max-w-[90vw] headless-toolbar-enter"
      data-headless-toolbar="true"
      style={{
        top: position.top,
        left: Math.max(16, Math.min(position.left, window.innerWidth - 16)),
        transform: 'translate(-50%, -100%)',
        marginTop: '-8px',
        overflow: 'visible' // Prevent scrolling, allow content to overflow
      }}
    >
      <TooltipProvider>
        <div className="flex items-center gap-1 min-w-max">
          {/* Mobile: Show only essential tools */}
          <div className="flex items-center gap-1 sm:hidden">
            {headlessToolbarGroups[0].items.map((item, itemIndex) => {
              const IconComponent = item.icon;
              const isActive = isFormatActive(item);
              console.log(`🎨 Mobile button ${item.type}${item.value ? ` (${item.value})` : ''} isActive:`, isActive);
              return (
                <Tooltip key={itemIndex}>
                  <TooltipTrigger>
                    <button
                      type="button"
                      ref={(el) => {
                        if (el) {
                          applyActiveStyles(el, isActive);
                        }
                      }}
                      style={getButtonStyle(isActive)}
                      className={getButtonClassName(isActive)}
                      onMouseEnter={(e) => {
                        if (!isActive) {
                          e.currentTarget.style.setProperty('background-color', '#f1f5f9', 'important');
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (!isActive) {
                          e.currentTarget.style.setProperty('background-color', 'transparent', 'important');
                        } else {
                          // Restore active state styling
                          applyActiveStyles(e.currentTarget, true);
                        }
                      }}
                      onClick={() => handleToolbarAction(item)}
                      title={item.label}
                    >
                      <IconComponent style={{
                        width: '12px',
                        height: '12px',
                        color: isActive ? 'hsl(var(--primary-foreground))' : 'inherit'
                      }} />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">
                    <p>{item.label}</p>
                  </TooltipContent>
                </Tooltip>
              );
            })}
            <div className="w-px h-6 bg-border mx-1" />
            {headlessToolbarGroups[1].items.slice(0, 2).map((item, itemIndex) => {
              const IconComponent = item.icon;
              const isActive = isFormatActive(item);
              console.log(`🎨 Mobile block button ${item.type}${item.value ? ` (${item.value})` : ''} isActive:`, isActive);
              return (
                <Tooltip key={itemIndex}>
                  <TooltipTrigger>
                    <button
                      type="button"
                      ref={(el) => {
                        if (el) {
                          applyActiveStyles(el, isActive);
                        }
                      }}
                      style={getButtonStyle(isActive)}
                      className={getButtonClassName(isActive)}
                      onMouseEnter={(e) => {
                        if (!isActive) {
                          e.currentTarget.style.setProperty('background-color', '#f1f5f9', 'important');
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (!isActive) {
                          e.currentTarget.style.setProperty('background-color', 'transparent', 'important');
                        } else {
                          // Restore active state styling
                          applyActiveStyles(e.currentTarget, true);
                        }
                      }}
                      onClick={() => handleToolbarAction(item)}
                      title={item.label}
                    >
                      <IconComponent style={{
                        width: '12px',
                        height: '12px',
                        color: isActive ? 'hsl(var(--primary-foreground))' : 'inherit'
                      }} />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">
                    <p>{item.label}</p>
                  </TooltipContent>
                </Tooltip>
              );
            })}
          </div>

          {/* Desktop: Show all tools */}
          <div className="hidden sm:flex items-center gap-1">
            {headlessToolbarGroups.map((group, groupIndex) => (
              <React.Fragment key={group.name}>
                {groupIndex > 0 && <div className="w-px h-6 bg-border mx-1" />}
                <div className="flex items-center gap-1 relative">
                  {group.items.map((item, itemIndex) => {
                    const IconComponent = item.icon;
                    const isColorButton = item.type === 'color' || item.type === 'background';
                    const isActive = isFormatActive(item);
                    console.log(`🎨 Desktop button ${item.type}${item.value ? ` (${item.value})` : ''} isActive:`, isActive, 'className will be:', isActive ? 'headless-toolbar-button-active' : 'normal');
                    return (
                      <div key={itemIndex} className="relative">
                        <Tooltip>
                          <TooltipTrigger>
                            <button
                              type="button"
                              ref={(el) => {
                                if (el) {
                                  applyActiveStyles(el, isActive);
                                  // Store color button refs for positioning
                                  if (item.type === 'color') {
                                    colorButtonRef.current = el;
                                  } else if (item.type === 'background') {
                                    backgroundButtonRef.current = el;
                                  }
                                }
                              }}
                              style={getButtonStyle(isActive)}
                              className={getButtonClassName(isActive)}
                              onMouseEnter={(e) => {
                                if (!isActive) {
                                  e.currentTarget.style.setProperty('background-color', '#f1f5f9', 'important');
                                }
                              }}
                              onMouseLeave={(e) => {
                                if (!isActive) {
                                  e.currentTarget.style.setProperty('background-color', 'transparent', 'important');
                                } else {
                                  // Restore active state styling
                                  applyActiveStyles(e.currentTarget, true);
                                }
                              }}
                              onClick={() => handleToolbarAction(item)}
                              title={item.label}
                            >
                              <IconComponent style={{
                                width: '16px',
                                height: '16px',
                                color: isActive ? 'hsl(var(--primary-foreground))' : 'inherit'
                              }} />
                            </button>
                          </TooltipTrigger>
                          <TooltipContent side="bottom">
                            <p>{item.label}</p>
                          </TooltipContent>
                        </Tooltip>
                        {isColorButton && showColorPicker === item.type && (
                          <ColorPicker
                            type={item.type as 'text' | 'background'}
                            onColorSelect={(color) => handleColorSelect(color, item.type as 'text' | 'background')}
                            onClose={() => setShowColorPicker(null)}
                            buttonRef={item.type === 'color' ? colorButtonRef : backgroundButtonRef}
                          />
                        )}
                      </div>
                    );
                  })}
                </div>
              </React.Fragment>
            ))}
          </div>

          <div className="w-px h-6 bg-border mx-1" />
          <Tooltip>
            <TooltipTrigger>
              <button
                type="button"
                style={getButtonStyle(false)}
                onMouseEnter={(e) => {
                  e.currentTarget.style.setProperty('background-color', '#f1f5f9', 'important');
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.setProperty('background-color', 'transparent', 'important');
                }}
                onClick={onClose}
                title="Close toolbar"
              >
                <X style={{
                  width: '16px',
                  height: '16px',
                  color: 'inherit'
                }} />
              </button>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>Close toolbar</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </TooltipProvider>
    </Card>
  );
}

export interface HeadlessQuillEditorProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  placeholder?: string;
  readOnly?: boolean;
  showHeadlessToolbar?: boolean;
}

export default function HeadlessQuillEditor({ 
  value, 
  onChange, 
  className, 
  placeholder, 
  readOnly,
  showHeadlessToolbar = true
}: HeadlessQuillEditorProps) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const quillRef = useRef<any>(null);
  const [toolbarVisible, setToolbarVisible] = useState(false);
  const [toolbarPosition, setToolbarPosition] = useState({ top: 0, left: 0 });
  const [selectedText, setSelectedText] = useState("");
  const [activeFormats, setActiveFormats] = useState<ActiveFormats>({});

  // Debug logging
  console.log('🎨 HeadlessQuillEditor - toolbarVisible:', toolbarVisible, 'selectedText:', selectedText, 'showHeadlessToolbar:', showHeadlessToolbar);

  // Remove debug test - let's fix real selection detection

  const modules = useMemo(() => ({
    toolbar: false, // Disable default toolbar
  }), []);

  const handleSelectionChange = useCallback((range: { index: number; length: number } | null) => {
    console.log('🎨 HeadlessQuillEditor - handleSelectionChange called with range:', range);

    if (!quillRef.current || !showHeadlessToolbar) {
      console.log('🎨 HeadlessQuillEditor - No quill ref or headless toolbar disabled');
      setToolbarVisible(false);
      return;
    }

    // If no range or empty selection, hide toolbar
    if (!range || range.length === 0) {
      console.log('🎨 HeadlessQuillEditor - No range or empty selection, hiding toolbar');
      setToolbarVisible(false);
      return;
    }

    if (range.length > 0) {
      const quill = quillRef.current.getEditor();
      const text = quill.getText(range.index, range.length);
      setSelectedText(text);

      // Use a small delay to ensure the selection is fully established
      setTimeout(() => {
        try {
          // Get current formatting of the selection - use the original range passed to the function
          // instead of getting a fresh range which might be different
          const currentFormat = quill.getFormat(range);
          console.log('🎨 Headless editor - Current format detected:', currentFormat);
          console.log('🎨 Headless editor - Range used for format detection:', range);

          // Update active formats state with proper type checking
          const newActiveFormats = {
            bold: Boolean(currentFormat.bold),
            italic: Boolean(currentFormat.italic),
            underline: Boolean(currentFormat.underline),
            strike: Boolean(currentFormat.strike),
            header: typeof currentFormat.header === 'number' ? currentFormat.header : undefined,
            blockquote: Boolean(currentFormat.blockquote),
            'code-block': Boolean(currentFormat['code-block']),
            list: typeof currentFormat.list === 'string' ? currentFormat.list : undefined,
            align: typeof currentFormat.align === 'string' ? currentFormat.align : undefined,
            color: typeof currentFormat.color === 'string' ? currentFormat.color : undefined,
            background: typeof currentFormat.background === 'string' ? currentFormat.background : undefined,
            link: typeof currentFormat.link === 'string' ? currentFormat.link : undefined,
          };

          console.log('🎨 Headless editor - Setting active formats:', newActiveFormats);
          setActiveFormats(newActiveFormats);

          // Get the bounds of the selection using the original range
          const bounds = quill.getBounds(range.index, range.length);
          const editorRect = quill.container.getBoundingClientRect();

          // Calculate position with viewport constraints
          const viewportWidth = window.innerWidth;
          const toolbarWidth = 400; // Approximate toolbar width
          const toolbarHeight = 50; // Approximate toolbar height

          let left = editorRect.left + bounds.left + (bounds.width / 2) + window.scrollX;
          let top = editorRect.top + bounds.top + window.scrollY;

          // Ensure toolbar stays within viewport horizontally
          if (left + toolbarWidth / 2 > viewportWidth) {
            left = viewportWidth - toolbarWidth / 2 - 16;
          }
          if (left - toolbarWidth / 2 < 0) {
            left = toolbarWidth / 2 + 16;
          }

          // Ensure toolbar stays within viewport vertically
          if (top - toolbarHeight < window.scrollY) {
            top = editorRect.top + bounds.top + bounds.height + window.scrollY + 8;
          }

          setToolbarPosition({ top, left });
          console.log('🎨 HeadlessQuillEditor - Setting toolbar visible at position:', { top, left });
          setToolbarVisible(true);
        } catch (error) {
          console.error('Error calculating toolbar position:', error);
          setToolbarVisible(false);
        }
      }, 10); // Small delay to ensure selection is established
    }
  }, [showHeadlessToolbar]);

  useEffect(() => {
    // Wait for Quill to be fully initialized
    const initializeEventListeners = () => {
      if (!quillRef.current) {
        const timeoutId = setTimeout(initializeEventListeners, 100);
        return () => clearTimeout(timeoutId);
      }

      const quill = quillRef.current.getEditor();
      if (!quill) {
        const timeoutId = setTimeout(initializeEventListeners, 100);
        return () => clearTimeout(timeoutId);
      }

      const editorElement = quill.container;

      // Listen for selection changes
      quill.on('selection-change', handleSelectionChange);

      // Also listen for mouse events to catch selections
      const handleMouseUp = () => {
        setTimeout(() => {
          const selection = quill.getSelection();
          if (selection && selection.length > 0) {
            handleSelectionChange(selection);
          }
        }, 50);
      };

      const handleMouseDown = () => {
        setToolbarVisible(false);
      };

      const handleKeyUp = (e: KeyboardEvent) => {
        if (e.key.includes('Arrow') || e.shiftKey || e.ctrlKey || e.metaKey) {
          setTimeout(() => {
            const selection = quill.getSelection();
            if (selection && selection.length > 0) {
              handleSelectionChange(selection);
            } else if (selection && selection.length === 0) {
              setToolbarVisible(false);
            }
          }, 50);
        }
      };

      const handleDoubleClick = () => {
        setTimeout(() => {
          const selection = quill.getSelection();
          if (selection && selection.length > 0) {
            handleSelectionChange(selection);
          }
        }, 50);
      };

      editorElement.addEventListener('mouseup', handleMouseUp);
      editorElement.addEventListener('mousedown', handleMouseDown);
      editorElement.addEventListener('keyup', handleKeyUp);
      editorElement.addEventListener('dblclick', handleDoubleClick);

      return () => {
        quill.off('selection-change', handleSelectionChange);
        editorElement.removeEventListener('mouseup', handleMouseUp);
        editorElement.removeEventListener('mousedown', handleMouseDown);
        editorElement.removeEventListener('keyup', handleKeyUp);
        editorElement.removeEventListener('dblclick', handleDoubleClick);
      };
    };

    const cleanup = initializeEventListeners();
    return cleanup || (() => {});
  }, [handleSelectionChange]);

  return (
    <div className={cn("relative", className)}>
      <div className="border rounded-md overflow-hidden headless-quill">
        <ReactQuill
          ref={quillRef}
          value={value}
          onChange={onChange}
          modules={modules}
          theme="snow"
          placeholder={placeholder}
          readOnly={readOnly}
        />
      </div>
      
      {showHeadlessToolbar && (
        <HeadlessToolbar
          quillRef={quillRef}
          isVisible={toolbarVisible}
          onClose={() => setToolbarVisible(false)}
          position={toolbarPosition}
          activeFormats={activeFormats}
        />
      )}
    </div>
  );
}
