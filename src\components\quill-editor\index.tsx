"use client";
import React, { useMemo, useState, useRef, useCallback, useEffect } from "react";
import dynamic from "next/dynamic";
import { cn } from "@/lib/utils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { X, Palette, Type } from "lucide-react";
import "quill/dist/quill.snow.css";

// Type definitions for Quill
interface QuillInstance {
  getSelection(): { index: number; length: number } | null;
  format(name: string, value: string | boolean): void;
  root: HTMLElement & { __quill?: QuillInstance };
}



// Extend Window interface for Quill
declare global {
  interface Window {
    Quill?: typeof import('quill');
  }
}

// Dynamically import ReactQuill
const ReactQuill = dynamic(() => import("react-quill-new"), {
  ssr: false,
  loading: () => (
    <div className="quill-editor">
      <div className="h-32 bg-gray-50 border border-gray-200 rounded animate-pulse flex items-center justify-center">
        <span className="text-gray-400">Loading editor...</span>
      </div>
    </div>
  ),
});

// Custom color palette (same as headless-editor.tsx)
const colors = [
  '#000000', '#e60000', '#ff9900', '#ffff00', '#008a00', '#0066cc', '#9933ff',
  '#ffffff', '#facccc', '#ffebcc', '#ffffcc', '#cce8cc', '#cce0f5', '#ebd6ff',
  '#bbbbbb', '#f06666', '#ffc266', '#ffff66', '#66b966', '#66a3e0', '#c285ff',
  '#888888', '#a10000', '#b26b00', '#b2b200', '#006100', '#0047b2', '#6b24b2',
  '#444444', '#5c0000', '#663d00', '#666600', '#003700', '#002966', '#3d1466'
];

// Color picker component for main toolbar
interface ColorPickerProps {
  onColorSelect: (color: string) => void;
  onClose: () => void;
  type: 'text' | 'background';
}

function ColorPicker({ onColorSelect, onClose, type }: ColorPickerProps) {
  return (
    <>
      {/* Backdrop to close color picker when clicking outside */}
      <div
        className="fixed inset-0 z-[9998] bg-transparent"
        onClick={onClose}
      />
      <Card
        className="fixed p-2 shadow-lg border bg-background min-w-[200px] max-w-[250px]"
        style={{
          zIndex: 9999,
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          maxHeight: '80vh',
          overflowY: 'auto'
        }}
      >
        <div className="mb-2">
          <p className="text-xs font-medium text-muted-foreground">
            {type === 'text' ? 'Text Color' : 'Background Color'}
          </p>
        </div>
        <div className="grid grid-cols-7 gap-1 mb-2">
          {colors.map((color, index) => (
            <button
              key={index}
              type="button"
              className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
              style={{ backgroundColor: color }}
              onClick={() => {
                onColorSelect(color);
                onClose();
              }}
              title={color}
            />
          ))}
        </div>
        <div className="flex items-center gap-2 pt-2 border-t">
          <input
            type="color"
            className="w-8 h-6 rounded border cursor-pointer"
            onChange={(e) => {
              onColorSelect(e.target.value);
              onClose();
            }}
            title="Custom color"
          />
          <span className="text-xs text-muted-foreground">Custom</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="ml-auto h-6 w-6 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </Card>
    </>
  );
}

const toolbarOptions = [
  ["bold", "italic", "underline", "strike"],
  ["blockquote", "code-block"],
  ["link", "image", "video", "formula"],
  [{ header: 1 }, { header: 2 }, { header: 3 }, { header: 4 }],
  [{ list: "ordered" }, { list: "bullet" }, { list: "check" }],
  [{ script: "sub" }, { script: "super" }],
  [{ indent: "-1" }, { indent: "+1" }],
  [{ direction: "rtl" }],
  [{ size: ["small", false, "large", "huge"] }],
  [{ header: [1, 2, 3, 4, 5, 6, false] }],
  // No built-in color pickers - we'll use custom ones
  [{ font: [] }],
  [{ align: [] }],
  ["clean"],
];

// Map toolbar button to tooltip label
const TOOLTIP_LABELS: Record<string, string> = {
  bold: "Bold",
  italic: "Italic",
  underline: "Underline",
  strike: "Strikethrough",
  blockquote: "Blockquote",
  "code-block": "Code Block",
  link: "Insert Link",
  image: "Insert Image",
  video: "Insert Video",
  formula: "Insert Formula",
  header: "Header",
  list: "List",
  ordered: "Numbered List",
  bullet: "Bullet List",
  check: "Checklist",
  script: "Script",
  sub: "Subscript",
  super: "Superscript",
  indent: "Indent",
  direction: "Text Direction (RTL)",
  size: "Font Size",
  color: "Font Color",
  background: "Background Color",
  font: "Font Family",
  align: "Text Align",
  clean: "Remove Formatting",
  "-1": "Decrease Indent",
  "+1": "Increase Indent",
  "rtl": "Right to Left"
};

// Items that should be rendered as select dropdowns
const SELECT_ITEMS = new Set(['size', 'header', 'color', 'background', 'font', 'align']);

// Helper function to render select options properly
function renderSelectOptions(item: Record<string, unknown>, key: string) {
  if (!Object.prototype.hasOwnProperty.call(item, key)) {
    return null;
  }

  const value = item[key];
  
  if (Array.isArray(value)) {
    return value.map((val, idx) => {
      if (val === false) {
        return (
          <option value="" key={idx}>
            Normal
          </option>
        );
      }
      return (
        <option value={val} key={idx}>
          {key === 'header' && typeof val === 'number' ? `Heading ${val}` : 
           key === 'size' && val === 'small' ? 'Small' :
           key === 'size' && val === 'large' ? 'Large' :
           key === 'size' && val === 'huge' ? 'Huge' :
           String(val)}
        </option>
      );
    });
  }

  return null;
}

// Custom Toolbar with shadcn tooltips and custom color pickers
function QuillCustomToolbar() {
  const [showColorPicker, setShowColorPicker] = useState<'text' | 'background' | null>(null);

  // Get Quill instance from the DOM after it's mounted
  const getQuillInstance = useCallback((): QuillInstance | null => {
    // Try to get the Quill instance from the ReactQuill component
    const reactQuillElement = document.querySelector('.ql-container');
    if (reactQuillElement) {
      const quillEditor = reactQuillElement.querySelector('.ql-editor') as HTMLElement & { __quill?: QuillInstance };
      if (quillEditor && quillEditor.__quill) {
        return quillEditor.__quill;
      }
    }

    // Alternative method - look for Quill instance on window
    if (typeof window !== 'undefined' && window.Quill) {
      const editors = document.querySelectorAll('.ql-editor');
      for (const editor of editors) {
        const editorElement = editor as HTMLElement & { __quill?: QuillInstance };
        if (editorElement.__quill) {
          return editorElement.__quill;
        }
      }
    }

    // Last resort - try to find via toolbar
    const toolbar = document.querySelector('#quill-toolbar') as HTMLElement & { __quill?: QuillInstance };
    if (toolbar && toolbar.__quill) {
      return toolbar.__quill;
    }

    return null;
  }, []);

  const handleColorSelect = useCallback((color: string, type: 'text' | 'background') => {
    const quill = getQuillInstance();
    if (!quill) {
      console.warn('Quill instance not found for color selection');
      return;
    }

    const range = quill.getSelection();
    if (!range) {
      console.warn('No text selection found');
      return;
    }

    if (type === 'text') {
      quill.format('color', color);
    } else {
      quill.format('background', color);
    }

    quill.focus();
    setShowColorPicker(null); // Close the color picker after selection
  }, [getQuillInstance]);

  return (
    <div id="quill-toolbar">
      <TooltipProvider>
        {toolbarOptions.map((group, i) => (
          <span key={i} className="ql-formats">
            {group.map((item, j) => {
              if (typeof item === "string") {
                // String items are always buttons
                const label = TOOLTIP_LABELS[item] || item;
                return (
                  <Tooltip key={j}>
                    <TooltipTrigger asChild>
                      <button type="button" className={`ql-${item}`} aria-label={label} />
                    </TooltipTrigger>
                    <TooltipContent>{label}</TooltipContent>
                  </Tooltip>
                );
              } else {
                // Handle object items
                const key = Object.keys(item)[0];
                const value = (item as Record<string, unknown>)[key];

                // Check if this should be a select dropdown
                if (SELECT_ITEMS.has(key) && Array.isArray(value)) {
                  const label = TOOLTIP_LABELS[key] || key;
                  return (
                    <Tooltip key={j}>
                      <TooltipTrigger asChild>
                        <select className={`ql-${key}`} aria-label={label}>
                          <option value="">
                            {key === 'header' ? 'Normal' :
                             key === 'size' ? 'Normal' :
                             key === 'font' ? 'Sans Serif' :
                             key === 'align' ? 'Left' :
                             'Default'}
                          </option>
                          {renderSelectOptions(item, key)}
                        </select>
                      </TooltipTrigger>
                      <TooltipContent>{label}</TooltipContent>
                    </Tooltip>
                  );
                } else if (SELECT_ITEMS.has(key) && (Array.isArray(value) && value.length === 0)) {
                  // Handle empty arrays for font, align
                  const label = TOOLTIP_LABELS[key] || key;
                  return (
                    <Tooltip key={j}>
                      <TooltipTrigger asChild>
                        <select className={`ql-${key}`} aria-label={label}>
                          {/* These will be populated by Quill with theme defaults */}
                        </select>
                      </TooltipTrigger>
                      <TooltipContent>{label}</TooltipContent>
                    </Tooltip>
                  );
                } else {
                  // Everything else should be buttons (list, script, indent, direction)
                  const label = TOOLTIP_LABELS[key] || TOOLTIP_LABELS[String(value)] || `${key}: ${value}`;
                  return (
                    <Tooltip key={j}>
                      <TooltipTrigger asChild>
                        <button
                          type="button"
                          className={`ql-${key}`}
                          value={String(value)}
                          aria-label={label}
                        />
                      </TooltipTrigger>
                      <TooltipContent>{label}</TooltipContent>
                    </Tooltip>
                  );
                }
              }
            })}
          </span>
        ))}

        {/* Custom Color Picker Buttons */}
        <span className="ql-formats">
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                type="button"
                className="ql-color-custom"
                onClick={() => setShowColorPicker('text')}
                aria-label="Font Color"
              >
                <Palette className="w-4 h-4" />
              </button>
            </TooltipTrigger>
            <TooltipContent>Font Color</TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <button
                type="button"
                className="ql-background-custom"
                onClick={() => setShowColorPicker('background')}
                aria-label="Background Color"
              >
                <Type className="w-4 h-4" />
              </button>
            </TooltipTrigger>
            <TooltipContent>Background Color</TooltipContent>
          </Tooltip>
        </span>
      </TooltipProvider>

      {/* Color Picker Modal */}
      {showColorPicker && (
        <ColorPicker
          type={showColorPicker}
          onColorSelect={(color) => handleColorSelect(color, showColorPicker)}
          onClose={() => setShowColorPicker(null)}
        />
      )}
    </div>
  );
}

export interface QuillEditorProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  placeholder?: string;
  readOnly?: boolean;
}

export default function QuillEditor({ value, onChange, className, placeholder, readOnly }: QuillEditorProps) {
  const quillRef = useRef<ReactQuillRef>(null);

  const modules = useMemo(() => ({
    toolbar: {
      container: "#quill-toolbar"
    }
  }), []);

  // Effect to ensure Quill instance is accessible
  useEffect(() => {
    if (quillRef.current) {
      const quillInstance = quillRef.current.getEditor();
      if (quillInstance) {
        // Store reference for the toolbar to access
        const editor = quillInstance.root;
        if (editor) {
          editor.__quill = quillInstance;
        }
      }
    }
  }, [value]); // Re-run when value changes to ensure connection

  return (
    <div className={cn("quill-editor", className)}>
      <QuillCustomToolbar />
      <ReactQuill
        ref={quillRef}
        value={value}
        onChange={onChange}
        modules={modules}
        theme="snow"
        placeholder={placeholder}
        readOnly={readOnly}
      />
    </div>
  );
}